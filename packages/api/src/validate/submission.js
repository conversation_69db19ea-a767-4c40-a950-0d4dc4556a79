const Joi = require('joi');

/* callout validation */
module.exports = {
  /* Id validation */
  idSchema: Joi.object({
    id: Joi.string().required(),
  }),
  /* Email validation */
  emailSchema: Joi.object({
    email: Joi.string().required().trim(),
  }),

  createSchema: Joi.object({
    snapshotId: Joi.string().required(),
    calloutId: Joi.string().required(),
  }),

  /* Delete agent schema validation */
  deleteSchema: Joi.object({
    ids: Joi.array().required(),
  }),

  updateFeedbackSchema: Joi.object({
    feedback: Joi.string().trim().max(5000).required(),
  }),
  updateNoteSchema: Joi.object({
    note: Joi.string().trim().max(5000).required(),
  }),
  updateTypeSchema: Joi.object({
    type: Joi.string().valid('slate', 'submission').required(),
  }),

  updateActivitySchema: Joi.object({
    activityId: Joi.string().required(),
    action: Joi.string().valid('view', 'not_view').required(),
  }),
};

module.exports.listSchema = Joi.object({
  calloutId: Joi.string().optional(),
  projectId: Joi.string().optional(),
  userId: Joi.string().optional(),
  submittedBy: Joi.string().optional(),
  select: Joi.string().optional(),
});

module.exports.trackingUpdateSchema = Joi.object({
  status: Joi.string()
    .valid(
      'NEW',
      'REJECTED',
      'FEEDBACK_SENT', // for 'submission' type
      'AWAITING_FEEDBACK',
      'LETS_TALK',
      'NOT_INTERESTED',
      'TRACKING', // for 'slate' type
    )
    .optional(),
  isEmailSent: Joi.bool().optional(),
}).or('status', 'isEmailSent');
