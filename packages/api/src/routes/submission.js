const SubmissionController = require('../controllers/SubmissionController');
const submissionValidation = require('../validate/submission');
const { responseCodes } = require('../utils/respSchemaHandler');

module.exports = {
  plugin: {
    async register(server, options) {
      server.route([
        {
          method: 'POST',
          path: '/',
          options: {
            plugins: {
              'hapi-swagger': {
                security: [
                  {
                    AUTH0_TOKEN: [],
                  },
                ],
                responses: responseCodes([200, 400, 401, 404, 500]),
              },
            },
            tags: ['api', 'Submission'],
            validate: { payload: submissionValidation.createSchema },
            pre: [],
            handler: SubmissionController.create,
            description: 'Create a new submission',
          },
        },
      ]);
      server.route([
        {
          method: 'PATCH',
          path: '/{id}/type',
          options: {
            plugins: {
              'hapi-swagger': {
                security: [
                  {
                    AUTH0_TOKEN: [],
                  },
                ],
                responses: responseCodes([200, 400, 401, 404, 500]),
              },
            },
            tags: ['api', 'Submission'],
            description: 'Update submission to slate',
            validate: {
              params: submissionValidation.idSchema,
              payload: submissionValidation.updateTypeSchema,
            },
            pre: [],
            handler: SubmissionController.updateSubmissionType,
          },
        },
      ]);
      server.route([
        {
          method: 'POST',
          path: '/{id}/feedback',
          options: {
            tags: ['api', 'Submission'],
            plugins: {
              'hapi-swagger': {
                security: [
                  {
                    AUTH0_TOKEN: [],
                  },
                ],
                responses: responseCodes([200, 400, 401, 404, 500]),
              },
            },
            description: 'Add feedback to a submission',
            validate: {
              params: submissionValidation.idSchema,
              payload: submissionValidation.updateFeedbackSchema, // status/type logic enforced in controller
            },
            handler: SubmissionController.addFeedback,
          },
        },
      ]);
      server.route([
        {
          method: 'PATCH',
          path: '/{id}/note',
          options: {
            tags: ['api', 'Submission'],
            plugins: {
              'hapi-swagger': {
                security: [
                  {
                    AUTH0_TOKEN: [],
                  },
                ],
                responses: responseCodes([200, 400, 401, 404, 500]),
              },
            },
            description: 'Add note to a slate',
            validate: {
              params: submissionValidation.idSchema,
              payload: submissionValidation.updateNoteSchema,
            },
            handler: SubmissionController.addNote,
          },
        },
      ]);
      server.route([
        {
          method: 'GET',
          path: '/{id}',
          options: {
            plugins: {
              'hapi-swagger': {
                security: [
                  {
                    AUTH0_TOKEN: [],
                  },
                ],
                responses: responseCodes([200, 400, 401, 404, 500]),
              },
            },
            tags: ['api', 'Submission'],
            pre: [],
            validate: {
              params: submissionValidation.idSchema,
            },
            handler: SubmissionController.detail,
            description: `Get submission detail`,
          },
        },
      ]);
      server.route([
        {
          method: 'GET',
          path: '/',
          options: {
            plugins: {
              'hapi-swagger': {
                security: [
                  {
                    AUTH0_TOKEN: [],
                  },
                ],
                responses: responseCodes([200, 400, 401, 404, 500]),
              },
            },
            tags: ['api', 'Submission'],
            pre: [],
            validate: {},
            handler: SubmissionController.list,
            description: `List submissions. Use select for fields, e.g. projectId.name,snapshotId.name,status`,
          },
        },
      ]);

      server.route([
        {
          method: 'PATCH',
          path: '/{id}/tracking',
          options: {
            tags: ['api', 'Submission'],
            plugins: {
              'hapi-swagger': {
                security: [
                  {
                    AUTH0_TOKEN: [],
                  },
                ],
                responses: responseCodes([200, 400, 401, 404, 500]),
              },
            },
            description: 'Update submission tracking (status, isEmailSent)',
            validate: {
              params: submissionValidation.idSchema,
              payload: submissionValidation.trackingUpdateSchema, // status/type/isEmailSent logic enforced in controller
            },
            handler: SubmissionController.updateTracking,
          },
        },
      ]);
    },
    version: process.env.API_VERSION,
    name: 'submission',
  },
};
