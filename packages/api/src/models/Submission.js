const mongoose = require('mongoose');
const { Schema } = mongoose;
const { ObjectId } = Schema;
const mongoosePaginate = require('mongoose-paginate');

const UserRefSchema = new Schema({
  _id: false,
  userId: { type: ObjectId, required: true },
  email: { type: String },
  username: { type: String },
  avatar: { type: String },
  role: {
    type: String,
    enum: ['cupid', 'collaborator', 'user'],
    default: 'user',
  },
});

const FeedbackSchema = new Schema({
  feedback: { type: String },
  addedAt: { type: Date, default: Date.now },
  addedBy: UserRefSchema,
  entity: { type: String }, // Will store the type (e.g., 'submission' or 'slate')
});

const ActivitySchema = new Schema({
  type: { type: String, enum: ['slate'], required: true },
  action: { type: String, enum: ['view', 'not_view'], default: 'not_view' },
  actionViewed: { type: Date, default: null },
  addedAt: { type: Date, default: Date.now },
});

const SubmissionSchema = new Schema(
  {
    calloutId: { type: ObjectId, ref: 'Callout', required: true },
    projectId: { type: ObjectId, ref: 'Project', required: true },
    snapshotId: { type: ObjectId, ref: 'ProjectSnaps', required: true },

    projectCreator: UserRefSchema,
    snapshotCreator: UserRefSchema,
    submittedBy: UserRefSchema,

    feedback: [FeedbackSchema],
    activities: [ActivitySchema],

    status: {
      type: String,
      enum: [
        'NEW',
        'REJECTED',
        'FEEDBACK_SENT',
        'LETS_TALK',
        'NOT_INTERESTED',
        'TRACKING',
      ], // Only for 'submission' type. 'slate' type statuses are enforced in logic.
      default: 'NEW',
    },
    type: {
      type: String,
      enum: ['submission', 'slate'],
      default: 'submission',
    },
    note: { type: String },
    isEmailSent: { type: Boolean, default: false },
    addedAt: { type: Date, default: Date.now },
    slateUpdatedBy: UserRefSchema,
    slateUpdatedAt: { type: Date },
  },
  { timestamps: true },
);

SubmissionSchema.plugin(mongoosePaginate);

module.exports =
  mongoose.models.Submission || mongoose.model('Submission', SubmissionSchema);
