import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import VideoEmbed from 'sharedComponents/videoEmbed/VideoEmbed';
import Style from '../Style/video.module.scss';
import TrapezoidLeft from '../trapezoid';

class BCSVideo extends PureComponent {
  render() {
    const { videos, isOpenInfoModal, snapStatus } = this.props;

    const customStyles = {
      containerClass: 'col-12 m0Auto',
      videoClass: `${Style.youtubeVideo}`,
      imageClass: `${Style.imagebox}`,
      playButtonProps: {
        width: '80px',
        height: '60px',
      },
      titleClass: 'p font-weight-bold text-white text-center mb-2',
      descClass: 'p text-white text-center',
    };

    const renderWrapper = (content) => (
      <>
        <TrapezoidLeft
          renderComponent={content}
          sectionName="PROJECT VIDEOS"
          trapezoidType="right"
          bgColour="bg-secondary"
          headerStatus
          isOpenInfoModal={isOpenInfoModal}
          infoIconStatus={!snapStatus}
        />
      </>
    );

    return (
      <VideoEmbed
        videos={videos}
        customStyles={customStyles}
        renderWrapper={renderWrapper}
      />
    );
  }
}

BCSVideo.propTypes = {
  videos: PropTypes.array.isRequired,
  isOpenInfoModal: PropTypes.func.isRequired,
  snapStatus: PropTypes.bool.isRequired,
};

export default BCSVideo;
