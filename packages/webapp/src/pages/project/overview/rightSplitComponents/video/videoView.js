import React from 'react';
import PropTypes from 'prop-types';
import VideoEmbed from 'sharedComponents/videoEmbed/VideoEmbed';
import Style from '../../style/projectDashboard.module.scss';

// Component show project videos

class VideoView extends React.PureComponent {
  render() {
    const { videos } = this.props;

    const customStyles = {
      containerClass: 'm0Auto',
      videoClass: `${Style.youtubeVideo}`,
      imageClass: Style.imagebox,
      playButtonProps: {
        width: '800px',
        height: '60px',
      },
      titleClass: `${Style.videoTitle}`,
      descClass: `${Style.videoDesc}`,
    };

    const renderWrapper = (videoElements) => (
      <div className="pt-4">
        {videoElements.map((videoElement, index) => (
          <div key={index} className={`${Style.showformVideosRow}`}>
            <div className="m0Auto">
              <div className={`${Style.videoImageContainer}`}>
                <div>{videoElement}</div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );

    return (
      <VideoEmbed
        videos={videos}
        customStyles={customStyles}
        renderWrapper={renderWrapper}
      />
    );
  }
}

VideoView.propTypes = {
  videos: PropTypes.array.isRequired,
};

export default VideoView;
