import {
  Show,
  SimpleShowLayout,
  TextField,
  Button,
  FunctionField,
  ChipField,
  useShowController,
  useNotify,
  useDataProvider,
  Confirm,
  useRefresh,
  Link,
  Datagrid,
  List,
  BooleanField,
  useRedirect,
  Title,
  EmailField,
  TopToolbar,
  ExportButton,
} from 'react-admin';
import {
  Box,
  Typography,
  Stack,
  IconButton,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import {
  Edit,
  Visibility,
  Share,
  Email,
  Delete,
  Add,
  ChatBubbleOutline,
  Close,
} from '@mui/icons-material';
import { get } from 'lodash';

import {
  getStatusBackgroundColor,
  getStatusTextColor,
} from '../../helpers/helper';
import InboxIcon from '@mui/icons-material/Inbox';
import { useEffect, useState } from 'react';
import PopoverModal from './popoverModal';
import StatusField from './slatesStatusDropdown';
import { PostPagination } from '../../helpers/helper';

export const ViewCallout = (props) => {
  const { record, isLoading } = useShowController();
  const [isOpen, setIsOpen] = useState(false);
  const [confirmSubmissionModal, setConfirmSubmissionModal] = useState(false);
  const [editNotes, setEditNotes] = useState(null);
  const [selectedSubmissionId, setSelectedSubmissionId] = useState(null);
  const [isOpenModal, setIsOpenModal] = useState(false);
  const [submissionData, setSubmissionData] = useState(null);
  const [isModeType, setModeType] = useState('');
  const [feedbackData, setFeedbackData] = useState(null);
  const [reset, setReset] = useState(false);
  const [confirmDeleteSlateModal, setConfirmDeleteSlateModal] = useState(false);
  const [deleteSlateId, setDeleteSlateId] = useState(null);
  const [showNewOnly, setShowNewOnly] = useState(false);
  const [submissionList, setSubmissionList] = useState([]);

  // const { data: calloutSlats, loading } = useGetList('calloutSlats');

  const redirect = useRedirect();

  const refresh = useRefresh();
  const notify = useNotify();
  const dataProvider = useDataProvider();

  useEffect(() => {
    if (record?.id) {
      // Fetch slates
      dataProvider
        .getList('submissionList', {
          filter: { calloutId: record.id, type: 'slate' },
          pagination: { page: 1, perPage: 1000000 },
          sort: { field: 'addedAt', order: 'DESC' },
        })
        .then(({ data }) => {
          setSubmissionList(data);
        })
        .catch((error) => {
          console.error('Error fetching submissionList:', error);
        });
    }
  }, [record, dataProvider]);

  if (isLoading) return <p>Loading...</p>;
  if (!record) return <p>No data available</p>;

  const isPublished = record?.isPublished;
  const confirmTitle = isPublished
    ? 'Are you sure you want to unpublish this call out?'
    : 'Are you sure you want to publish this call out?';
  const confirmContent = isPublished
    ? 'Unpublishing this call out will make it no longer display in the Smash platform and stop users submitting their projects.'
    : 'Publishing this call out will display it in the Smash platform and allow users to submit their projects.';

  const handleSendUpgradeEmail = async () => {
    if (!record.discoverer || !record.id) {
      alert('Missing required data for API request.');
      return;
    }
    const payload = {
      email: record.email,
      planId: record.enterPrisePlanId,
      type: 'enterprise',
      priceId: record.enterPrisePriceId,
    };
    try {
      const enterPriseLink = await dataProvider.create(
        'v1/subscription/getStripeCheckoutLink',
        {
          data: payload,
        }
      );
      const discovererEmailPayload = {
        id: record.id,
        buttonLink: enterPriseLink.data.url,
      };
      await dataProvider.create(
        'v1/callout/discovererEmail',
        { data: discovererEmailPayload },
        {
          onSuccess: () => {
            notify('Email sent successfully!', {
              type: 'success',
              multiLine: true,
              autoHideDuration: 2500,
            });
            refresh();

            // Notify after everything is done
            notify('Upgrade email sent successfully!', {
              type: 'success',
              autoHideDuration: 2500,
            });
          },
          onError: (error) => {
            notify(`${String(error)}`, {
              type: 'error',
              multiLine: true,
              autoHideDuration: 2500,
            });
          },
        }
      );
    } catch (error) {
      console.error('API Error:', error);
      notify('Failed to send upgrade email.', {
        type: 'error',
        autoHideDuration: 2500,
      });
    }
  };

  const handleViewCallout = () => {
    window.open(
      `${process.env.REACT_APP_WEBAPP_URL}/callouts/view/${record.id}`,
      '_blank'
    );
  };

  const handleShareCallout = () => {
    const link = `${process.env.REACT_APP_WEBAPP_URL}/callouts/view/${record.id}`;
    navigator.clipboard
      .writeText(link)
      .then(() => {
        notify('Callout link copied successfully', { type: 'success' });
      })
      .catch(() => {
        notify('Failed to copy the link', { type: 'warning' });
      });
  };

  const handleRejectClick = (id) => {
    setSelectedSubmissionId(id); // Store the selected submission ID
    setConfirmSubmissionModal(true); // Open the confirm modal
  };

  const clickHandler = (val, event) => {
    switch (event) {
      case 'addToSlate': {
        setSubmissionData(val);
        setEditNotes(null); // Clear previous notes
        setFeedbackData(null); // Clear previous feedback
        setIsOpenModal(true);
        setModeType('addToSlate');
        setReset(false);
        break;
      }
      case 'addFeedback': {
        setFeedbackData(val);
        setEditNotes(null); // Clear previous notes
        setSubmissionData(null); // Clear previous submission
        setIsOpenModal(true);
        setModeType('addFeedback');
        setReset(false);
        break;
      }
      default: {
        console.log('default event', event);
      }
    }
  };

  const confirmRejectSubmission = () => {
    if (selectedSubmissionId) {
      rejectSubmission(selectedSubmissionId);
    }
    setConfirmSubmissionModal(false);
  };

  const handlePublishCallout = async () => {
    if (!record.discoverer || !record.id) {
      alert('Missing required data for API request.');
      return;
    }

    const payload = {
      isPublished: !isPublished,
    };

    try {
      await dataProvider.update('v1/callout', {
        method: 'PATCH',
        id: record.id,
        data: payload,
      });
      setIsOpen(false);
      refresh();
      notify(`Callout updated successfully!`);
    } catch (error) {
      setIsOpen(false);
      console.error('API Error:', error);
      notify(`Failed to ${isPublished ? 'published' : 'unpublished'} callout.`);
    }
  };

  const rejectSubmission = async (id) => {
    try {
      await dataProvider.update('submissionStatus', {
        id,
        data: { status: 'REJECTED' },
      });
      setIsOpen(false);
      refresh();
      notify(`Submission rejected successfully!`);
    } catch (error) {
      setIsOpen(false);
      console.error('API Error:', error);
      notify(`Failed to reject submission.`);
    }
  };

  const handleView = (records) => {
    window.open(
      `${process.env.REACT_APP_WEBAPP_URL}/project/snap/${records.snapshotHash}`,
      '_blank'
    );
  };

  const handleFeedbackSuccess = async (data) => {
    const id = feedbackData.id;
    try {
      await dataProvider.create('submissionFeedback', {
        id,
        data: { feedback: data },
      });
      // Only update status to FEEDBACK_SENT for submissions, not slates
      const entityType =
        feedbackData.entity || feedbackData.type || 'submission';
      if (entityType === 'submission') {
        await dataProvider.update(`v1/submission/${id}/tracking`, {
          data: { status: 'FEEDBACK_SENT' },
        });
      }
      setIsOpenModal(false);
      refresh();
      notify(`Submission feedback successfully!`);
      setReset(true);
    } catch (error) {
      setIsOpenModal(false);
      console.error('API Error:', error);
      notify(`Failed to submit submission feedback.`);
    }
  };

  const handleSubmissionSuccess = async (data) => {
    const id = isModeType !== 'editNotes' ? submissionData.id : editNotes.id;
    try {
      if (isModeType === 'addToSlate') {
        await dataProvider.update('submissionType', {
          id,
          data: { type: 'slate' },
        });
        await dataProvider.update(`v1/submission/${id}/note`, {
          method: 'PATCH',
          data: { note: data },
        });
      } else if (isModeType === 'editNotes') {
        // PATCH note to v1/submission/{id}/note
        await dataProvider.update(`v1/submission/${id}/note`, {
          method: 'PATCH',
          data: { note: data },
        });
      }
      setIsOpenModal(false);
      refresh();
      notify('Submitted successfully!');
      setReset(true);
    } catch (error) {
      setIsOpenModal(false);
      console.error('API Error:', error);
      notify('Failed to submit submission.');
    }
  };

  //handle submit
  const handleSubmit = (data) => {
    if (isModeType === 'addToSlate' || isModeType === 'editNotes') {
      handleSubmissionSuccess(data);
    } else if (
      isModeType === 'addFeedback' ||
      isModeType === 'addSlateFeedback'
    ) {
      handleFeedbackSuccess(data);
    }
  };

  const handleStatusChange = async (newStatus, records) => {
    try {
      await dataProvider.update(`v1/submission/${records.id}/tracking`, {
        data: { status: newStatus },
      });
      refresh();
      notify('Status Updated successfully!');
    } catch (error) {
      console.error('API Error:', error);
      notify('Failed to update status.');
    }
  };

  const handleEditSlat = (val, event) => {
    switch (event) {
      case 'editNotes': {
        setEditNotes(val);
        setFeedbackData(null);
        setSubmissionData(null);
        setIsOpenModal(true);
        setModeType('editNotes');
        setReset(false);
        break;
      }
      case 'addFeedback': {
        setFeedbackData(val);
        setEditNotes(null); // Clear previous notes
        setSubmissionData(null); // Clear previous submission
        setIsOpenModal(true);
        setModeType('addSlateFeedback');
        setReset(false);
        break;
      }
      // case 'deleteSubmission': {
      //   setRejectedData({ isOpen: true, record: val });
      //   break;
      // }
      default: {
        console.log('default event', event);
      }
    }
  };
  // Use submissionList and its isEmailSent key for disabling logic
  const isEmailButtonDisabled = () => {
    if (submissionList.length === 0) return true;
    return submissionList.every((item) => {
      return item.isEmailSent === true;
    });
  };

  const emailSentSlate = async () => {
    try {
      await dataProvider.update(`v1/callout`, {
        method: 'PATCH',
        id: record.id,
        endPoint: 'emailSentSlate',
      });
      refresh();
      notify(`Status Updated successfully!`);
    } catch (error) {
      console.error('API Error:', error);
      notify(`Failed to update status.`);
    }
  };

  const removeFromSlates = async () => {
    try {
      await dataProvider.update('submissionType', {
        id: deleteSlateId,
        data: { type: 'submission' },
      });
      refresh();
      notify(`Slate deleted successfully!`);
      setConfirmDeleteSlateModal(false);
    } catch (error) {
      setConfirmDeleteSlateModal(false);
      console.error('API Error:', error);
      notify(`Failed to delete slate.`);
    }
  };
  //export button
  const CallOutListActions = () => (
    <TopToolbar>
      <ExportButton maxResults={1000000} />
    </TopToolbar>
  );

  return (
    <>
      <Title title={`Callout: ${record.name}`} />
      <Show title={false}>
        <SimpleShowLayout>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
            Call out details
          </Typography>
          <Box
            sx={{ mt: 4, p: 2, border: '1px solid #ddd', borderRadius: '5px' }}
          >
            <Stack
              direction="row"
              spacing={2}
              sx={{ mb: 3, justifyContent: 'space-between' }}
            >
              <Stack direction="row" spacing={4}>
                <Button
                  label="Edit call out"
                  startIcon={<Edit />}
                  onClick={() => {
                    redirect(`/callouts/${record.id}`);
                  }}
                />
                <Button
                  label={isPublished ? 'Unpublish' : 'Publish'}
                  onClick={() => setIsOpen(true)}
                  startIcon={<Visibility />}
                />
                <Confirm
                  isOpen={isOpen}
                  title={confirmTitle}
                  content={confirmContent}
                  confirm={isPublished ? 'UnPublish' : 'Publish'}
                  onConfirm={handlePublishCallout}
                  onClose={() => setIsOpen(false)}
                />
                <Button
                  label="Share call out"
                  startIcon={<Share />}
                  onClick={() => handleShareCallout()}
                />
                <Button
                  label="View call out"
                  startIcon={<Visibility />}
                  onClick={() => handleViewCallout()}
                />
              </Stack>
              <Button
                label="Send upgrade email"
                startIcon={<Email />}
                onClick={handleSendUpgradeEmail}
                sx={{ color: 'white', p: 1 }}
              />
            </Stack>
            <Box
              sx={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: 3,
                '& > *': {
                  flexBasis: 'calc(25% - 24px)',
                  maxWidth: 'calc(25% - 24px)',
                },
              }}
            >
              <Box>
                <Typography variant="body2" fontWeight="bold" sx={{ mb: 2 }}>
                  Call out name
                </Typography>
                <TextField source="name" />
              </Box>

              <Box>
                <Typography variant="body2" fontWeight="bold" sx={{ mb: 2 }}>
                  Discoverer name
                </Typography>
                <TextField source="discoverer" />
              </Box>

              <Box>
                <Typography variant="body2" fontWeight="bold" sx={{ mb: 2 }}>
                  Organization name
                </Typography>
                <TextField source="companyName" />
              </Box>

              <Box>
                <Typography variant="body2" fontWeight="bold" sx={{ mb: 2 }}>
                  Discoverer email
                </Typography>
                <TextField source="email" />
              </Box>

              <Box>
                <Typography variant="body2" fontWeight="bold" sx={{ mb: 1 }}>
                  Subscription
                </Typography>
                <FunctionField
                  label={
                    <span style={{ fontWeight: 'bold' }}>Subscription</span>
                  }
                  render={(record) =>
                    record ? (
                      <ChipField
                        source="subscription"
                        record={record}
                        style={{
                          backgroundColor: getStatusBackgroundColor(
                            record.subscription
                          ),
                          textTransform: 'capitalize',
                          color: getStatusTextColor(record.subscription),
                          fontWeight: '700',
                        }}
                      />
                    ) : null
                  }
                />
                <PopoverModal
                  title={
                    isModeType === 'editNotes' || isModeType === 'addToSlate'
                      ? `Update ${get(editNotes || submissionData, 'projectName', '')} to Slate`
                      : `Send Feedback to ${get(feedbackData, 'projectCreator', get(feedbackData, 'creatorName', ''))}`
                  }
                  open={isOpenModal}
                  onClose={() => {
                    setIsOpenModal(false);
                    setEditNotes(null);
                    setFeedbackData(null);
                    setSubmissionData(null);
                  }}
                  btnText={
                    isModeType === 'editNotes' || isModeType === 'addToSlate'
                      ? 'Update Slate'
                      : 'Send to project creator'
                  }
                  onSubmit={handleSubmit}
                  defaultValue={
                    isModeType === 'editNotes'
                      ? get(submissionList, 'note', '')
                      : null
                  }
                  placeholder={
                    isModeType === 'editNotes' || isModeType === 'addToSlate'
                      ? 'Update note about call out for discover'
                      : 'Send feedback to the project creator so they can update their project and submit a new snapshot'
                  }
                  previousFeedback={get(feedbackData, 'feedback', []).filter(
                    (fb) =>
                      fb?.entity ===
                      (isModeType === 'addSlateFeedback'
                        ? 'slate'
                        : 'submission')
                  )}
                  reset={reset}
                />

                <Confirm
                  isOpen={confirmSubmissionModal}
                  title={'Reject Submission'}
                  content={
                    'Are you sure you want to change this submission status?'
                  }
                  confirm={'Confirm'}
                  onConfirm={confirmRejectSubmission} // Calls the function when confirmed
                  onClose={() => setConfirmSubmissionModal(false)} // Closes modal
                />
                <Confirm
                  isOpen={confirmDeleteSlateModal}
                  title={'Delete Slate'}
                  content={'Are you sure you want to delete this slate?'}
                  confirm={'Confirm'}
                  onConfirm={removeFromSlates} // Calls the function when confirmed
                  onClose={() => setConfirmDeleteSlateModal(false)} // Closes modal
                />
              </Box>

              {record.geners && (
                <Box>
                  <Typography variant="body2" fontWeight="bold" sx={{ mb: 2 }}>
                    Opportunity type
                  </Typography>
                  <TextField source="genres" />
                </Box>
              )}

              {record.opportunities && (
                <Box>
                  <Typography variant="body2" fontWeight="bold" sx={{ mb: 2 }}>
                    Genres
                  </Typography>
                  <TextField source="opportunities" />
                </Box>
              )}
            </Box>
          </Box>

          <Box
            sx={{ mt: 2, p: 2, border: '1px solid #ddd', borderRadius: '5px' }}
          >
            <Stack
              direction="row"
              spacing={2}
              sx={{ justifyContent: 'space-between' }}
            >
              <Typography variant="subtitle1" fontWeight="bold">
                Slates
              </Typography>
              <Button
                label="Send slate updates to discoverer"
                startIcon={<Email />}
                onClick={emailSentSlate}
                sx={{
                  color: 'white', // Ensures text is white
                  p: 1,
                  opacity: isEmailButtonDisabled() ? 0.5 : 1,
                  '&.Mui-disabled': {
                    color: 'rgba(255, 255, 255, 0.5)', // Ensures disabled text is visible
                  },
                }}
                disabled={isEmailButtonDisabled()}
              />
            </Stack>
            {/* --- NEW SLATES FLOW: v1/submission API --- */}
            <List
              actions={<CallOutListActions />}
              disableSyncWithLocation
              resource="submissionList"
              filter={{ calloutId: record?.id, type: 'slate' }}
              title={false}
              empty={
                <>
                  <Box textAlign="center" p={2}>
                    <InboxIcon sx={{ fontSize: 100 }} />
                    <Typography variant="h6" color="textSecondary">
                      No Slates Yet
                    </Typography>
                  </Box>
                </>
              }
              pagination={<PostPagination />}
              meta={{ noPagination: props.perPage === 100000 }}
            >
              <Datagrid bulkActionButtons={false}>
                <FunctionField
                  sortable={false}
                  source="snapshotName"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Snapshot name</span>
                  }
                  render={(record) => (
                    <Link
                      className="admin-link"
                      to={`${process.env.REACT_APP_WEBAPP_URL}/project/snap/${record.snapshotHash}?status=${record.submissionsStatus}&id=${record.id}`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {record.snapshotName}
                    </Link>
                  )}
                />
                <FunctionField
                  sortable={false}
                  source="projectName"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Project Name</span>
                  }
                  render={(record) => (
                    <Link
                      className="admin-link"
                      to={`/projects/${record.projectId}/show`}
                    >
                      {record.projectName}
                    </Link>
                  )}
                />
                <FunctionField
                  sortBy="projectCreator"
                  source="projectCreator"
                  label={
                    <span style={{ fontWeight: 'bold' }}> Project creator</span>
                  }
                  render={(record) => (
                    <Link
                      className="admin-link"
                      to={`/users/${record.projectCreatorId}/show`}
                    >
                      {record.projectCreator}
                    </Link>
                  )}
                />
                <EmailField
                  source="creatorEmail"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Creator email</span>
                  }
                />
                <FunctionField
                  label={
                    <span style={{ fontWeight: 'bold' }}>Subscription</span>
                  }
                  render={(record) =>
                    record ? (
                      <ChipField
                        source="subscription"
                        record={record}
                        style={{
                          backgroundColor: getStatusBackgroundColor(
                            record.subscription
                          ),
                          textTransform: 'capitalize',
                          color: getStatusTextColor(record.subscription),
                          fontWeight: '700',
                        }}
                      />
                    ) : null
                  }
                />
                <TextField
                  sortBy="addedAt"
                  label={<span style={{ fontWeight: 'bold' }}>Created</span>}
                  source="addedAt"
                />
                <TextField
                  sortBy="addedAt"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Added to slate</span>
                  }
                  source="addedAt"
                />
                <FunctionField
                  sortable={false}
                  label={<span style={{ fontWeight: 'bold' }}>Status</span>}
                  render={(record) => {
                    // Default status for slates: 'awaitingFeedback' if not set
                    const status = record.status || 'AWAITING_FEEBACK';
                    return (
                      <StatusField
                        record={{ ...record, status }}
                        onStatusChange={handleStatusChange}
                      />
                    );
                  }}
                />
                <BooleanField
                  label={<span style={{ fontWeight: 'bold' }}>Email Sent</span>}
                  source="isEmailSent"
                />
                <BooleanField
                  label={<span style={{ fontWeight: 'bold' }}>Viewed</span>}
                  source="viewed"
                />
                <FunctionField
                  label={<span style={{ fontWeight: 'bold' }}>Actions</span>}
                  render={(record) => (
                    <div style={{ display: 'flex', gap: '8px' }}>
                      <IconButton
                        onClick={() => handleEditSlat(record, 'editNotes')}
                      >
                        <Edit style={{ color: '#0D0D3F' }} />
                      </IconButton>
                      <IconButton
                        onClick={() => handleEditSlat(record, 'addFeedback')}
                      >
                        <ChatBubbleOutline style={{ color: '#0D0D3F' }} />
                      </IconButton>
                      <IconButton
                        onClick={() => {
                          setConfirmDeleteSlateModal(true);
                          setDeleteSlateId(record.id);
                        }}
                      >
                        <Delete style={{ color: '#0D0D3F' }} />
                      </IconButton>
                    </div>
                  )}
                />
              </Datagrid>
            </List>
          </Box>

          <Box
            sx={{ mt: 2, p: 2, border: '1px solid #ddd', borderRadius: '5px' }}
          >
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="subtitle1" fontWeight="bold">
                Submissions
              </Typography>
              <FormControlLabel
                control={
                  <Checkbox
                    color="secondary"
                    checked={showNewOnly}
                    onChange={(e) => setShowNewOnly(e.target.checked)}
                  />
                }
                label="Show New Only"
              />
            </Box>
            <List
              disableSyncWithLocation
              actions={<CallOutListActions />}
              resource="submissionList"
              filter={{
                calloutId: record?.id,
                type: 'submission',
                status: showNewOnly ? 'NEW' : '',
              }}
              empty={
                <>
                  <Box textAlign="center" p={2}>
                    <InboxIcon sx={{ fontSize: 100 }} />
                    <Typography variant="h6" color="textSecondary">
                      No Submissions Yet
                    </Typography>
                  </Box>
                </>
              }
              title={false}
              pagination={<PostPagination />}
              meta={{ noPagination: props.perPage === 100000 }}
            >
              <Datagrid bulkActionButtons={false}>
                <FunctionField
                  sortable={false}
                  source="snapshotName"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Snapshot name</span>
                  }
                  render={(record) => (
                    <Link
                      className="admin-link"
                      to={`${process.env.REACT_APP_WEBAPP_URL}/project/snap/${record.snapshotHash}`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {record.snapshotName}
                    </Link>
                  )}
                />

                <FunctionField
                  sortable={false}
                  source="projectName"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Project Name</span>
                  }
                  render={(record) => (
                    <Link
                      className="admin-link"
                      to={`/projects/${record.projectId}/show`}
                    >
                      {record.projectName}
                    </Link>
                  )}
                />
                <FunctionField
                  sortBy="submittedBy"
                  source="projectCreator"
                  label={
                    <span style={{ fontWeight: 'bold' }}> Project creator</span>
                  }
                  render={(record) => (
                    <Link
                      className="admin-link"
                      to={`/users/${record.projectCreatorId}/show`}
                    >
                      {record.projectCreator}
                    </Link>
                  )}
                />
                <FunctionField
                  label={
                    <span style={{ fontWeight: 'bold' }}>Subscription</span>
                  }
                  render={(record) =>
                    record ? (
                      <ChipField
                        source="subscription"
                        record={record}
                        style={{
                          backgroundColor: getStatusBackgroundColor(
                            record.subscription
                          ),
                          textTransform: 'capitalize',
                          color: getStatusTextColor(record.subscription),
                          fontWeight: '700',
                        }}
                      />
                    ) : null
                  }
                />

                <EmailField
                  source="creatorEmail"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Creator email</span>
                  }
                />
                <TextField
                  sortable={false}
                  label={<span style={{ fontWeight: 'bold' }}>genre</span>}
                  source="genre"
                />
                <TextField
                  sortBy="addedAt"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Date submitted</span>
                  }
                  source="addedAt"
                />
                <FunctionField
                  sortBy="status"
                  label={<span style={{ fontWeight: 'bold' }}>Status</span>}
                  render={(record) => {
                    if (!record) return null;

                    const subsStatus = record.status;
                    const submittedByRole = record.submittyedByRole || '';
                    const entityType =
                      record.entity || record.type || 'submission';

                    let statusLabel = subsStatus;

                    // Default status labels
                    if (subsStatus === 'cupid_selected') {
                      statusLabel = 'Cupid Selected';
                    } else if (subsStatus === 'FEEDBACK_SENT') {
                      if (entityType === 'submission') {
                        statusLabel = 'Feedback Sent';
                      } else {
                        statusLabel = subsStatus;
                      }
                    } else if (subsStatus === 'NEW') {
                      statusLabel = 'New submission';
                    }

                    // Cupid Submission override
                    if (submittedByRole === 'cupid' && subsStatus === 'NEW') {
                      statusLabel = 'Cupid Submission';
                    }

                    return (
                      <ChipField
                        source="status"
                        record={{ ...record, status: statusLabel }}
                        style={{
                          backgroundColor: getStatusBackgroundColor(
                            subsStatus,
                            submittedByRole
                          ),
                          textTransform: 'capitalize',
                          color: getStatusTextColor(
                            subsStatus,
                            submittedByRole
                          ),
                          fontWeight: '700',
                        }}
                      />
                    );
                  }}
                />

                <FunctionField
                  label={<span style={{ fontWeight: 'bold' }}>Actions</span>}
                  render={(record) => (
                    <div style={{ display: 'flex', gap: '8px' }}>
                      {/* View Icon */}
                      <IconButton onClick={() => handleView(record)}>
                        <Visibility style={{ color: '#0D0D3F' }} />
                      </IconButton>

                      {/* Close Icon (Trigger Confirm Modal) */}
                      <IconButton onClick={() => handleRejectClick(record.id)}>
                        <Close style={{ color: '#0D0D3F' }} />
                      </IconButton>

                      {/* Chat Icon */}
                      <IconButton
                        onClick={() => clickHandler(record, 'addFeedback')}
                      >
                        <ChatBubbleOutline style={{ color: '#0D0D3F' }} />
                      </IconButton>

                      {/* Add Icon */}
                      <IconButton
                        onClick={() => clickHandler(record, 'addToSlate')}
                      >
                        <Add style={{ color: '#0D0D3F' }} />
                      </IconButton>
                    </div>
                  )}
                />
              </Datagrid>
            </List>
          </Box>
        </SimpleShowLayout>
      </Show>
    </>
  );
};
